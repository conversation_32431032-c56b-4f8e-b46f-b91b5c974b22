/* CSS Document */

/* Reset and base styles */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	background-color: #656568;
	background-image: url('../img/page_bg.jpg');
	font-family: Arial, sans-serif;
	font-size: 12px;
	line-height: 1.4;
}

/* Page wrapper */
#page-wrapper {
	width: 800px;
	margin: 30px auto;
	background-color: #FFFFFF;
	border: 3px solid #4F4F4E;
}

/* Header */
#header {
	height: 150px;
	background-image: url('../img/header.jpg');
	background-repeat: no-repeat;
	background-position: center;
	position: relative;
	color: #FFFFFF;
	text-align: left;
	padding: 20px 30px;
}

#header h1 {
	font-size: 36px;
	font-weight: bold;
	letter-spacing: 8px;
	margin-bottom: 5px;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

#header h2 {
	font-size: 14px;
	font-weight: normal;
	letter-spacing: 2px;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

/* Content wrapper */
#content-wrapper {
	display: flex;
	min-height: 400px;
}

/* Main content */
#main-content {
	width: 450px;
	padding: 20px;
	background-color: #FCF8E3;
}

.section {
	margin-bottom: 20px;
}

.section h3 {
	color: #A0410D;
	font-size: 14px;
	font-weight: bold;
	margin-bottom: 8px;
	text-transform: uppercase;
}

.section p {
	color: #333333;
	text-align: justify;
	margin-bottom: 10px;
}

/* Sidebar */
#sidebar {
	width: 350px;
	background-color: #605A38;
	padding: 20px;
	color: #FFFFFF;
}

.sidebar-section h4 {
	background-color: #3A3822;
	color: #FFFFFF;
	font-size: 12px;
	font-weight: bold;
	padding: 5px 10px;
	margin-bottom: 15px;
	text-transform: uppercase;
}

.sidebar-section p {
	color: #FFFFFF;
	font-weight: bold;
	margin: 10px 0 5px 0;
}

.sidebar-section ul {
	list-style: none;
	margin-bottom: 15px;
}

.sidebar-section li {
	margin-bottom: 3px;
	padding-left: 15px;
	position: relative;
}

.sidebar-section li:before {
	content: "■";
	color: #FFFFFF;
	position: absolute;
	left: 0;
}

.sidebar-section a {
	color: #FFFFFF;
	text-decoration: underline;
}

.sidebar-section a:hover {
	color: #FCF8E3;
}

/* Footer */
#footer {
	height: 60px;
	background-image: url('../img/footer.jpg');
	background-repeat: no-repeat;
	background-position: center;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #FFFFFF;
	font-size: 11px;
}
